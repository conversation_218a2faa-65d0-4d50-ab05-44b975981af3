# Google Search Plugin Configuration Example
# This file shows the required environment variables for the Google Search plugin.
# Copy this file to config.env and fill in your actual values.

# (Optional) The port for the proxy server if you need one for the API requests.
GOOGLE_PROXY_PORT=7890

# (Required) Your Google Custom Search API Key.
# You can get this from the Google Cloud Console.
GOOGLE_SEARCH_API=YOUR_GOOGLE_API_KEY_HERE

# (Required) Your Google Custom Search Engine ID (cx).
# This specifies which search engine to use for the queries.
GOOGLE_CX=321bfcadad2434124