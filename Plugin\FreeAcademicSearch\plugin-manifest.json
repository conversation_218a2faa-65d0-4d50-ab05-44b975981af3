{"manifestVersion": "1.0.0", "name": "FreeAcademicSearch", "displayName": "免费学术搜索插件", "version": "1.0.0", "description": "使用免费API进行学术论文搜索，整合arXiv和Semantic Scholar等数据源，无需API密钥。", "author": "VCP User", "pluginType": "synchronous", "entryPoint": {"type": "nodejs", "command": "node FreeAcademicSearch.js"}, "communication": {"protocol": "stdio", "timeout": 60000}, "capabilities": {"invocationCommands": [{"commandIdentifier": "FreeAcademicSearch", "description": "使用免费API进行学术论文搜索，整合多个数据源。无需API密钥，支持关键词搜索。请使用以下格式：\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」FreeAcademicSearch「末」,\nquery:「始」(必需) 搜索关键词或问题「末」,\nmax_results:「始」(可选, 默认20) 最大结果数量，范围 1-50「末」\n<<<[END_TOOL_REQUEST]>>>", "example": "<<<[TOOL_REQUEST]>>>\ntool_name:「始」FreeAcademicSearch「末」,\nquery:「始」machine learning deep neural networks「末」,\nmax_results:「始」15「末」\n<<<[END_TOOL_REQUEST]>>>"}]}, "configSchema": {"DEBUG_MODE": {"type": "boolean", "required": false, "default": true, "description": "启用调试模式以获得详细日志输出"}}, "permissions": ["fetch_data_from_url"], "dependencies": {"node": ">=14.0.0", "libraries": ["node-fetch"]}}