四.文件管理
1.文件搜索，基于Everything模块实现。
tool_name:「始」LocalSearchController「末」,
command:「始」search「末」,
query:「始」VCP a.txt「末」, //语法和Everything一致，支持高级语法，支持多tag混合搜索。
maxResults:「始」50「末」

2.文件管理器。可以管理用户指定文件区域，也可以阅读全局文件。支持富文本格式读取。
tool_name:「始」FileOperator「末」,
①查看工作目录下所有文件列表。
command:「始」ListAllowedDirectories「末」
②阅读文件，支持阅读电脑里任何区域文件，支持阅读多媒体文件，word/pdf文档等等富格式文件。
command:「始」ReadFile「末」,
filePath:「始」/path/to/your/document.pdf「末」
③写入文件（同名自动重命名）
command:「始」WriteFile「末」,
filePath:「始」/path/to/your/file.txt「末」,
content:「始」这是要写入的新内容。
这是第二行。「末」
④追加文件内容。
command:「始」AppendFile「末」,
filePath:「始」/path/to/your/log.txt「末」,
content:「始」
在文件末尾追加内容「末」
⑤编辑文件，编辑后会覆盖已有内容
command:「始」EditFile「末」,
filePath:「始」/path/to/existing_file.txt「末」,
content:「始」这是覆盖后的新内容。「末」
⑥List指定目录
command:「始」ListDirectory「末」,
directoryPath:「始」/path/to/directory「末」,
showHidden:「始」false「末」 //是否返回隐藏文件
⑦查询文件元数据（如大小、创建时间、修改时间、是否是目录等）
command:「始」FileInfo「末」,
filePath:「始」/path/to/your/file.txt「末」
⑧复制文件
command:「始」CopyFile「末」,
sourcePath:「始」/path/to/source.txt「末」,
destinationPath:「始」/path/to/destination.txt「末」
⑨移动文件
command:「始」MoveFile「末」,
sourcePath:「始」/path/to/source.txt「末」,
destinationPath:「始」/path/to/new_directory/source.txt「末」
⑩重命名文件
command:「始」RenameFile「末」,
sourcePath:「始」/path/to/old_name.txt「末」,
destinationPath:「始」/path/to/new_name.txt「末」
11.删除文件
command:「始」DeleteFile「末」,
filePath:「始」/path/to/deletable_file.txt「末」
12.创建文件夹
command:「始」CreateDirectory「末」,
directoryPath:「始」/path/to/new_folder/sub_folder「末」
13.编辑文件
command:「始」ApplyDiff「末」,
filePath:「始」/path/to/your/file.txt「末」,
searchString:「始」旧内容「末」,
replaceString:「始」新内容「末」
14.构建批量指令——FileOperator支持批量指令例如：
<<<[TOOL_REQUEST]>>>
tool_name:「始」FileOperator「末」,
command1:「始」RenameFile「末」,
sourcePath1:「始」/path/to/old_name.txt「末」,
destinationPath1:「始」/path/to/new_name.txt「末」
command2:「始」RenameFile「末」,
sourcePath2:「始」/path/to/old_name.txt「末」,
destinationPath2:「始」/path/to/new_name.txt「末」
command3:「始」EditFile「末」,
filePath3:「始」/path/to/existing_file.txt「末」,
content3:「始」这是覆盖后的新内容。「末」
<<<[END_TOOL_REQUEST]>>>
通过这种方式可以构建多组指令。

