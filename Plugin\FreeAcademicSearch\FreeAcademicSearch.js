const fs = require('fs').promises;
const path = require('path');

/**
 * 免费学术搜索插件 - 使用多个免费API进行学术论文搜索
 * 支持 arXiv, Semantic Scholar 等免费数据源
 */

const LOG_PREFIX = '[FreeAcademicSearch]';

class FreeAcademicSearchPlugin {
    constructor() {
        this.debugMode = true; // 默认开启调试模式
    }

    /**
     * 搜索 arXiv 论文
     */
    async searchArxiv(query, maxResults = 10) {
        try {
            const { default: fetch } = await import('node-fetch');
            
            // 构建 arXiv API URL
            const baseUrl = 'http://export.arxiv.org/api/query';
            const params = new URLSearchParams({
                search_query: `all:${query}`,
                start: 0,
                max_results: maxResults,
                sortBy: 'submittedDate',
                sortOrder: 'descending'
            });
            
            const url = `${baseUrl}?${params.toString()}`;
            
            if (this.debugMode) {
                console.error(`${LOG_PREFIX} Searching arXiv: ${url}`);
            }
            
            const response = await fetch(url);
            if (!response.ok) {
                throw new Error(`arXiv API error: ${response.status}`);
            }
            
            const xmlText = await response.text();
            return this.parseArxivXML(xmlText);
            
        } catch (error) {
            console.error(`${LOG_PREFIX} arXiv search failed:`, error.message);
            return [];
        }
    }

    /**
     * 搜索 Semantic Scholar 论文
     */
    async searchSemanticScholar(query, maxResults = 10) {
        try {
            const { default: fetch } = await import('node-fetch');
            
            const url = 'https://api.semanticscholar.org/graph/v1/paper/search';
            const params = new URLSearchParams({
                query: query,
                limit: Math.min(maxResults, 100),
                fields: 'paperId,title,authors,year,abstract,venue,citationCount,url,openAccessPdf'
            });
            
            const fullUrl = `${url}?${params.toString()}`;
            
            if (this.debugMode) {
                console.error(`${LOG_PREFIX} Searching Semantic Scholar: ${fullUrl}`);
            }
            
            const response = await fetch(fullUrl, {
                headers: {
                    'User-Agent': 'VCPToolBox-FreeAcademicSearch/1.0'
                }
            });
            
            if (!response.ok) {
                throw new Error(`Semantic Scholar API error: ${response.status}`);
            }
            
            const data = await response.json();
            return this.parseSemanticScholarResponse(data);
            
        } catch (error) {
            console.error(`${LOG_PREFIX} Semantic Scholar search failed:`, error.message);
            return [];
        }
    }

    /**
     * 解析 arXiv XML 响应
     */
    parseArxivXML(xmlText) {
        const papers = [];
        
        // 简单的XML解析（实际项目中建议使用专门的XML解析库）
        const entryRegex = /<entry>([\s\S]*?)<\/entry>/g;
        let match;
        
        while ((match = entryRegex.exec(xmlText)) !== null) {
            const entryXml = match[1];
            
            const title = this.extractXMLValue(entryXml, 'title');
            const summary = this.extractXMLValue(entryXml, 'summary');
            const published = this.extractXMLValue(entryXml, 'published');
            const id = this.extractXMLValue(entryXml, 'id');
            
            // 提取作者
            const authorRegex = /<author><name>(.*?)<\/name><\/author>/g;
            const authors = [];
            let authorMatch;
            while ((authorMatch = authorRegex.exec(entryXml)) !== null) {
                authors.push(authorMatch[1]);
            }
            
            papers.push({
                source: 'arXiv',
                title: title?.replace(/\s+/g, ' ').trim(),
                authors: authors.join('; '),
                abstract: summary?.replace(/\s+/g, ' ').trim(),
                publishDate: published?.split('T')[0],
                url: id,
                venue: 'arXiv preprint',
                citationCount: 'N/A',
                doi: 'N/A',
                pdfUrl: id ? id.replace('http://arxiv.org/abs/', 'http://arxiv.org/pdf/') + '.pdf' : null
            });
        }
        
        return papers;
    }

    /**
     * 解析 Semantic Scholar 响应
     */
    parseSemanticScholarResponse(data) {
        if (!data.data) return [];
        
        return data.data.map(paper => ({
            source: 'Semantic Scholar',
            title: paper.title,
            authors: paper.authors ? paper.authors.map(a => a.name).join('; ') : 'N/A',
            abstract: paper.abstract || 'N/A',
            publishDate: paper.year ? paper.year.toString() : 'N/A',
            url: paper.url,
            venue: paper.venue || 'N/A',
            citationCount: paper.citationCount || 0,
            doi: 'N/A',
            pdfUrl: paper.openAccessPdf ? paper.openAccessPdf.url : null
        }));
    }

    /**
     * 从XML中提取值
     */
    extractXMLValue(xml, tagName) {
        const regex = new RegExp(`<${tagName}[^>]*>(.*?)<\/${tagName}>`, 's');
        const match = xml.match(regex);
        return match ? match[1] : null;
    }

    /**
     * 主搜索函数 - 合并多个数据源
     */
    async search(searchParams) {
        const { query, max_results = 20 } = searchParams;
        
        if (!query) {
            throw new Error('Search query is required');
        }
        
        const resultsPerSource = Math.ceil(max_results / 2);
        
        // 并行搜索多个数据源
        const [arxivResults, semanticResults] = await Promise.allSettled([
            this.searchArxiv(query, resultsPerSource),
            this.searchSemanticScholar(query, resultsPerSource)
        ]);
        
        let allResults = [];
        
        if (arxivResults.status === 'fulfilled') {
            allResults = allResults.concat(arxivResults.value);
        }
        
        if (semanticResults.status === 'fulfilled') {
            allResults = allResults.concat(semanticResults.value);
        }
        
        // 按发表日期排序（最新的在前）
        allResults.sort((a, b) => {
            const dateA = new Date(a.publishDate);
            const dateB = new Date(b.publishDate);
            return dateB - dateA;
        });
        
        // 限制结果数量
        return allResults.slice(0, max_results);
    }
}

// 主函数 - VCP 插件入口
async function main() {
    try {
        const input = process.argv[2] || await new Promise(resolve => {
            let data = '';
            process.stdin.on('data', chunk => data += chunk);
            process.stdin.on('end', () => resolve(data));
        });
        
        const params = JSON.parse(input);
        const plugin = new FreeAcademicSearchPlugin();
        
        const results = await plugin.search(params);
        
        const output = {
            status: 'success',
            result: {
                papers: results,
                total_found: results.length,
                search_query: params.query,
                timestamp: new Date().toISOString()
            }
        };
        
        console.log(JSON.stringify(output));
        
    } catch (error) {
        const output = {
            status: 'error',
            result: null,
            error: error.message
        };
        console.log(JSON.stringify(output));
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}

module.exports = FreeAcademicSearchPlugin;
