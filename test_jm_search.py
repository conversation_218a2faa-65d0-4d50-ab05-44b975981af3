#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试 JMCrawl 插件搜索功能
"""

import subprocess
import json
import sys
import os

def test_jm_search():
    """测试 JM 搜索功能"""
    print("🔍 开始测试 JMCrawl 搜索功能...")
    
    # 测试搜索请求
    search_request = {
        "command": "SearchAlbum",
        "keyword": "调教",
        "page": 1,
        "limit": 5
    }
    
    plugin_path = os.path.join("Plugin", "JMCrawl", "jmcrawl.py")
    
    try:
        print(f"📋 搜索关键词: {search_request['keyword']}")
        print(f"📄 页码: {search_request['page']}")
        print(f"🔢 限制数量: {search_request['limit']}")
        print("⏳ 正在搜索...")
        
        # 启动插件进程
        process = subprocess.Popen(
            [sys.executable, plugin_path],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            encoding='utf-8',
            errors='replace',
            cwd=os.getcwd()
        )
        
        # 发送搜索请求
        input_data = json.dumps(search_request, ensure_ascii=False)
        stdout, stderr = process.communicate(input=input_data, timeout=60)
        
        print("=" * 50)
        print("📤 发送的请求:")
        print(input_data)
        print("=" * 50)
        
        # 显示标准输出
        if stdout.strip():
            print("📥 插件响应 (stdout):")
            try:
                result = json.loads(stdout.strip())
                print(json.dumps(result, ensure_ascii=False, indent=2))
                
                if result.get('status') == 'success':
                    print("\n✅ 搜索成功！")
                else:
                    print(f"\n❌ 搜索失败: {result.get('error', 'Unknown error')}")
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
                print("原始输出:")
                print(stdout)
        else:
            print("❌ 没有标准输出")
        
        # 显示错误输出
        if stderr.strip():
            print("=" * 50)
            print("🔍 调试信息 (stderr):")
            print(stderr)
        
        print("=" * 50)
        print(f"🏁 进程退出码: {process.returncode}")
        
    except subprocess.TimeoutExpired:
        print("⏰ 搜索超时")
        process.kill()
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")

if __name__ == "__main__":
    test_jm_search()
