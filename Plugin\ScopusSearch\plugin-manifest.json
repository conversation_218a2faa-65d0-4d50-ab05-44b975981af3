{"manifestVersion": "1.0.0", "name": "ScopusSearch", "displayName": "Scopus 学术搜索插件", "version": "1.0.0", "description": "基于Scopus API的学术论文检索插件，支持静态推送和实时搜索功能，包含智能PDF下载策略。", "author": "<PERSON><PERSON><PERSON>", "pluginType": "synchronous", "entryPoint": {"command": "node ScopusSearch.js"}, "communication": {"protocol": "stdio", "timeout": 60000}, "refreshIntervalCron": "*/30 * * * *", "capabilities": {"systemPromptPlaceholders": [{"placeholder": "{{ScopusDailyPapersData}}", "description": "来自Scopus的最新学术论文数据，每30分钟更新一次。"}], "invocationCommands": [{"commandIdentifier": "ScopusSearch", "description": "使用Scopus API进行学术论文搜索。支持关键词、作者、期刊、日期范围等多维度搜索，可选择下载PDF文件。请使用以下精确格式请求搜索：\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」ScopusSearch「末」,\nquery:「始」(必需) 搜索关键词或问题「末」,\nauthor:「始」(可选) 作者姓名「末」,\njournal:「始」(可选) 期刊名称「末」,\ndate_range:「始」(可选) 日期范围，格式: YYYY-MM-DD:YYYY-MM-DD「末」,\nmax_results:「始」(可选, 默认20) 最大结果数量，范围 1-100「末」,\ndownload_pdf:「始」(可选, 默认false) 是否尝试下载PDF文件「末」\n<<<[END_TOOL_REQUEST]>>>", "example": "```text\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」ScopusSearch「末」,\nquery:「始」machine learning deep neural networks「末」,\nauthor:「始」张三「末」,\ndate_range:「始」2023-01-01:2024-12-31「末」,\nmax_results:「始」10「末」,\ndownload_pdf:「始」true「末」\n<<<[END_TOOL_REQUEST]>>>\n```"}]}, "configSchema": {"SCOPUS_API_KEY": {"type": "string", "required": true, "description": "Scopus API密钥，用于访问Scopus数据库"}, "SCOPUS_SEARCH_TERMS": {"type": "string", "required": false, "default": "\"artificial intelligence\" OR \"machine learning\" OR \"deep learning\"", "description": "静态推送功能的默认搜索关键词"}, "SCOPUS_MAX_RESULTS": {"type": "number", "required": false, "default": 50, "description": "单次搜索的最大结果数量"}, "SCOPUS_DAYS_RANGE": {"type": "number", "required": false, "default": 7, "description": "静态推送获取的天数范围"}, "PDF_DOWNLOAD_ENABLED": {"type": "boolean", "required": false, "default": true, "description": "是否启用PDF下载功能"}, "PDF_DOWNLOAD_PATH": {"type": "string", "required": false, "default": "./scopus_papers", "description": "PDF文件保存路径"}, "THIRD_PARTY_ENABLED": {"type": "boolean", "required": false, "default": false, "description": "是否启用第三方PDF下载服务"}, "SCIHUB_MIRROR": {"type": "string", "required": false, "default": "https://sci-hub.se", "description": "Sci-Hub镜像地址"}, "SCOPUS_DEBUG_MODE": {"type": "boolean", "required": false, "default": false, "description": "启用调试模式以获得更详细的日志输出"}}, "permissions": ["fetch_data_from_url", "write_to_file_system", "read_from_file_system", "create_directories"], "cacheFiles": ["scopus_papers_cache.json", "pdf_download_log.json"], "logLevel": "info"}