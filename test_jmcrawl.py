#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JMCrawl 插件测试脚本
用于验证 JMCrawl 插件是否正确安装和配置
"""

import json
import subprocess
import sys
import os

def test_jmcrawl_plugin():
    """测试 JMCrawl 插件"""
    print("🧪 开始测试 JMCrawl 插件...")
    
    # 测试数据
    test_requests = [
        {
            "name": "搜索测试",
            "data": {
                "command": "SearchAlbum",
                "keyword": "恋爱",
                "page": 1,
                "limit": 3
            }
        },
        {
            "name": "获取信息测试",
            "data": {
                "command": "GetAlbumInfo",
                "album_id": "422866"
            }
        }
    ]
    
    plugin_path = os.path.join("Plugin", "JMCrawl", "jmcrawl.py")
    
    if not os.path.exists(plugin_path):
        print(f"❌ 插件文件不存在: {plugin_path}")
        return False
    
    success_count = 0
    total_tests = len(test_requests)
    
    for i, test in enumerate(test_requests, 1):
        print(f"\n📋 测试 {i}/{total_tests}: {test['name']}")
        print(f"   请求数据: {test['data']}")
        
        try:
            # 启动插件进程，使用 UTF-8 编码
            process = subprocess.Popen(
                [sys.executable, plugin_path],
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8',
                errors='replace',
                cwd=os.getcwd()
            )
            
            # 发送测试数据
            input_data = json.dumps(test['data'], ensure_ascii=False)
            stdout, stderr = process.communicate(input=input_data, timeout=30)
            
            # 解析结果
            if stdout.strip():
                try:
                    result = json.loads(stdout.strip())
                    if result.get('status') == 'success':
                        print(f"   ✅ 测试成功")
                        print(f"   📄 结果预览: {result.get('result', '')[:100]}...")
                        success_count += 1
                    else:
                        print(f"   ⚠️  插件返回错误: {result.get('error', 'Unknown error')}")
                        print(f"   💡 AI消息: {result.get('messageForAI', 'No message')}")
                except json.JSONDecodeError as e:
                    print(f"   ❌ JSON解析失败: {e}")
                    print(f"   📄 原始输出: {stdout[:200]}...")
            else:
                print(f"   ❌ 无输出")
            
            # 显示错误信息（如果有）
            if stderr.strip():
                print(f"   🔍 调试信息: {stderr.strip()[:200]}...")
                
        except subprocess.TimeoutExpired:
            print(f"   ⏰ 测试超时")
            process.kill()
        except Exception as e:
            print(f"   ❌ 测试异常: {str(e)}")
    
    print(f"\n📊 测试总结:")
    print(f"   ✅ 成功: {success_count}/{total_tests}")
    print(f"   ❌ 失败: {total_tests - success_count}/{total_tests}")
    
    if success_count == total_tests:
        print(f"   🎉 所有测试通过！JMCrawl 插件工作正常。")
        return True
    elif success_count > 0:
        print(f"   ⚠️  部分测试通过，插件基本可用但可能存在网络或配置问题。")
        return True
    else:
        print(f"   💥 所有测试失败，请检查依赖安装和网络连接。")
        return False

def check_dependencies():
    """检查依赖"""
    print("🔍 检查核心依赖...")

    try:
        import jmcomic
        from jmcomic import JmOption, DirRule
        print(f"   ✅ jmcomic (v{jmcomic.__version__})")
        print(f"   ✅ JmOption 和 DirRule 导入成功")
        return True
    except ImportError as e:
        print(f"   ❌ jmcomic 导入失败: {str(e)}")
        print(f"💡 请运行: pip install jmcomic>=2.6.0")
        return False

if __name__ == "__main__":
    print("🚀 JMCrawl 插件测试工具")
    print("=" * 50)
    
    # 检查依赖
    deps_ok = check_dependencies()
    
    if deps_ok:
        # 测试插件
        plugin_ok = test_jmcrawl_plugin()
        
        if plugin_ok:
            print(f"\n🎊 JMCrawl 插件已准备就绪！")
            print(f"💡 您现在可以在 VCP 系统中使用 JMCrawl 插件了。")
        else:
            print(f"\n🔧 插件需要进一步配置或调试。")
    else:
        print(f"\n📦 请先安装缺失的依赖。")
    
    print("=" * 50)
