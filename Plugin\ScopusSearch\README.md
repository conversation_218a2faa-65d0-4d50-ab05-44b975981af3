# Scopus学术搜索插件

基于Scopus API的学术论文检索插件，支持静态推送和实时搜索功能，包含智能PDF下载策略。

## 功能特性

### 🔄 混合模式支持
- **静态推送**：定期自动获取最新学术论文，更新系统占位符
- **实时搜索**：支持AGENT通过工具调用接口进行实时检索
- **智能PDF下载**：三层级下载策略（官方→第三方→仅信息）

### 🔍 强大的搜索功能
- 支持关键词、作者、期刊、日期范围等多维度搜索
- 灵活的搜索参数组合和过滤条件
- 标准化的数据输出格式，兼容现有插件生态

### 📁 PDF管理系统
- 自动文件命名和目录组织
- 重复文件检测和缓存机制
- 可配置的下载策略和存储路径

## 安装配置

### 1. 环境要求
- Node.js >= 14.0
- VCPToolBox插件系统
- 有效的Scopus API密钥

### 2. 配置步骤

1. **复制配置文件**
   ```bash
   cp Plugin/ScopusSearch/config.env.example Plugin/ScopusSearch/config.env
   ```

2. **编辑配置文件**
   ```bash
   # 必须配置
   SCOPUS_API_KEY=your_scopus_api_key_here
   
   # 可选配置
   SCOPUS_SEARCH_TERMS="artificial intelligence" OR "machine learning"
   SCOPUS_MAX_RESULTS=50
   PDF_DOWNLOAD_ENABLED=true
   PDF_DOWNLOAD_PATH=./scopus_papers
   ```

3. **获取Scopus API密钥**
   - 访问 [Elsevier Developer Portal](https://dev.elsevier.com/)
   - 注册账户并申请API密钥
   - 将密钥添加到配置文件中

## 使用方式

### 静态占位符使用

插件会自动定期获取论文数据，可通过以下占位符在系统提示中使用：

```
{{ScopusDailyPapersData}}
```

### AGENT工具调用

AGENT可以通过以下格式调用搜索功能：

```text
<<<[TOOL_REQUEST]>>>
tool_name:「始」ScopusSearch「末」,
query:「始」machine learning deep neural networks「末」,
author:「始」张三「末」,
journal:「始」Nature「末」,
date_range:「始」2023-01-01:2024-12-31「末」,
max_results:「始」10「末」,
download_pdf:「始」true「末」
<<<[END_TOOL_REQUEST]>>>
```

#### 参数说明

| 参数 | 类型 | 必需 | 说明 |
|------|------|------|------|
| `query` | String | ✅ | 搜索关键词或问题 |
| `author` | String | ❌ | 作者姓名 |
| `journal` | String | ❌ | 期刊名称 |
| `date_range` | String | ❌ | 日期范围，格式：YYYY-MM-DD:YYYY-MM-DD |
| `max_results` | Number | ❌ | 最大结果数量（1-100，默认20） |
| `download_pdf` | Boolean | ❌ | 是否尝试下载PDF（默认false） |

## 数据格式

插件返回标准化的JSON格式数据：

```json
{
  "scopus_id": "SCOPUS_ID_12345",
  "title": "论文标题",
  "authors": "作者1; 作者2; 作者3",
  "publication_date": "2024-01-15",
  "abstract": "论文摘要内容...",
  "doi": "10.1000/example.doi",
  "journal": "期刊名称",
  "cited_by_count": 25,
  "link": "https://www.scopus.com/record/display.uri?eid=...",
  "pdf_status": "downloaded|unavailable|not_attempted",
  "pdf_path": "/path/to/downloaded.pdf",
  "download_source": "official|third_party|none",
  "retrieved_at": "2024-01-15T10:30:00.000Z"
}
```

## PDF下载策略

### 三层级下载机制

1. **第一层：官方渠道**
   - 检查开放获取状态
   - 直接从出版商下载PDF

2. **第二层：第三方服务**（可选）
   - Sci-Hub或类似服务
   - 需要在配置中明确启用

3. **第三层：信息回退**
   - 仅提供论文元数据
   - 包含访问链接

### 文件管理

- **命名规范**：`{DOI_safe}_{title_safe}.pdf`
- **存储结构**：可配置的目录路径
- **重复检查**：基于文件名和内容哈希

## 调试和日志

启用调试模式可获得详细的执行日志：

```bash
SCOPUS_DEBUG_MODE=true
```

调试信息包括：
- API请求和响应详情
- PDF下载尝试过程
- 缓存操作记录
- 错误处理详情

## 常见问题

### Q: 如何获取Scopus API密钥？
A: 需要在Elsevier Developer Portal注册并申请。个人学术用途通常可以免费获得。

### Q: API有什么使用限制？
A: Scopus API有请求频率和配额限制，具体取决于您的API密钥类型。

### Q: PDF下载失败怎么办？
A: 插件会自动回退到提供论文信息。可以启用调试模式查看详细错误。

### Q: 如何自定义搜索条件？
A: 可以在配置文件中修改默认搜索词，或通过工具调用传递具体参数。

## 技术支持

如果遇到问题，请检查：
1. Scopus API密钥是否正确配置
2. 网络连接是否正常
3. VCPToolBox插件系统是否运行正常
4. 配置文件格式是否正确

更多技术细节请参考插件源码中的注释说明。

## 版本历史

- **v1.0.0** - 初始版本
  - 基本搜索功能
  - 静态推送支持
  - 工具调用接口
  - PDF下载策略框架

## 许可证

本插件遵循VCPToolBox项目的许可证条款。使用时请遵守Scopus API的使用条款和条件。