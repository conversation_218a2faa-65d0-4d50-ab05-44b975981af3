{"manifestVersion": "1.0.0", "name": "GoogleSearch", "version": "2.0.0", "displayName": "谷歌搜索 (API版)", "description": "一个使用Google Custom Search API进行搜索的同步插件。", "author": "Kilo Code", "pluginType": "synchronous", "entryPoint": {"type": "nodejs", "command": "node search.js"}, "communication": {"protocol": "stdio", "timeout": 15000}, "configSchema": {"GOOGLE_SEARCH_API": {"type": "string", "description": "谷歌自定义搜索API密钥。"}, "GOOGLE_CX": {"type": "string", "description": "谷歌自定义搜索引擎ID。"}, "GOOGLE_PROXY_PORT": {"type": "string", "description": "用于API请求的代理端口（可选）。"}}, "capabilities": {"invocationCommands": [{"commandIdentifier": "GoogleSearch", "description": "调用此工具在谷歌上执行搜索。\n参数:\n- query (字符串, 必需): 需要在谷歌上搜索的关键词。\n调用格式:\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」GoogleSearch「末」,\nquery:「始」如何学习编程？「末」\n<<<[END_TOOL_REQUEST]>>>", "example": "<<<[TOOL_REQUEST]>>>\ntool_name:「始」GoogleSearch「末」,\nquery:「始」VCP插件开发「末」\n<<<[END_TOOL_REQUEST]>>>"}]}}