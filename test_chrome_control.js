// 测试 ChromeControl 插件的脚本
// 用于验证不同命令的参数传递是否正确

const testCommands = [
    // 不需要 target 的命令
    {
        name: "debug_page",
        params: {
            command: "debug_page"
        }
    },
    {
        name: "get_links", 
        params: {
            command: "get_links"
        }
    },
    {
        name: "scroll_down",
        params: {
            command: "scroll",
            direction: "down",
            distance: 300
        }
    },
    {
        name: "find_search_boxes",
        params: {
            command: "find_search_boxes"
        }
    },
    {
        name: "capture_page",
        params: {
            command: "capture_page",
            include_elements: true
        }
    },
    
    // 需要 target 的命令（这些应该会失败，因为没有提供 target）
    {
        name: "click_without_target",
        params: {
            command: "click"
            // 故意不提供 target
        }
    },
    {
        name: "type_without_target", 
        params: {
            command: "type",
            text: "test text"
            // 故意不提供 target
        }
    }
];

console.log("ChromeControl 测试命令列表:");
console.log("=".repeat(50));

testCommands.forEach((test, index) => {
    console.log(`${index + 1}. ${test.name}:`);
    console.log(`   参数: ${JSON.stringify(test.params, null, 2)}`);
    console.log(`   预期: ${test.params.command === 'click' || test.params.command === 'type' ? '应该失败（缺少target）' : '应该成功'}`);
    console.log("");
});

console.log("使用方法:");
console.log("1. 确保 VCPChrome 扩展已连接到 VCP 服务器");
console.log("2. 在 AI 对话中使用以下格式测试命令:");
console.log("");
console.log("<<<[TOOL_REQUEST]>>>");
console.log("tool_name: 「始」ChromeControl「末」,");
console.log("command: 「始」debug_page「末」");
console.log("<<<[END_TOOL_REQUEST]>>>");
