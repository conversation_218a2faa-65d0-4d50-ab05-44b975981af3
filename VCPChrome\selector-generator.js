function getCssSelector(element) {
    if (!(element instanceof Element)) {
        return null;
    }

    const parts = [];
    while (element) {
        let part = element.tagName.toLowerCase();

        // Prioritize ID
        if (element.id) {
            part += `#${element.id}`;
            parts.unshift(part);
            break; // ID is unique, no need to go further
        }

        // Prioritize data-testid, data-cy, etc.
        const testId = Array.from(element.attributes).find(attr => attr.name.startsWith('data-test') || attr.name.startsWith('data-cy'));
        if (testId) {
            part += `[${testId.name}="${testId.value}"]`;
            parts.unshift(part);
            // This is likely unique enough, but we continue for robustness
        }

        // Add class names
        const classNames = Array.from(element.classList);
        if (classNames.length > 0) {
            part += `.${classNames.join('.')}`;
        }

        // Add position if needed
        if (element.parentElement) {
            const siblings = Array.from(element.parentElement.children);
            const sameTagSiblings = siblings.filter(sibling => sibling.tagName === element.tagName);
            if (sameTagSiblings.length > 1) {
                const index = sameTagSiblings.indexOf(element) + 1;
                part += `:nth-of-type(${index})`;
            }
        }

        parts.unshift(part);
        element = element.parentElement;
    }

    return parts.join(' > ');
}
function getXPath(element) {
    if (element.id) {
        return `//*[@id="${element.id}"]`;
    }
    if (element === document.body) {
        return '/html/body';
    }

    let ix = 0;
    const siblings = element.parentNode.childNodes;
    for (let i = 0; i < siblings.length; i++) {
        const sibling = siblings[i];
        if (sibling === element) {
            return `${getXPath(element.parentNode)}/${element.tagName.toLowerCase()}[${ix + 1}]`;
        }
        if (sibling.nodeType === 1 && sibling.tagName === element.tagName) {
            ix++;
        }
    }
}