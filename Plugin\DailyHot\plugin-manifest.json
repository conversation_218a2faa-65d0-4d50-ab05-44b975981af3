{"manifestVersion": "1.0.0", "name": "DailyHot", "displayName": "每日热榜", "version": "2.0.0", "description": "在后台周期性地获取所有主流平台的今日热榜信息，并通过占位符 {{VCPDailyHot}} 提供。", "author": "Kilo Code & Roo", "pluginType": "static", "entryPoint": {"type": "nodejs", "command": "node daily-hot.js"}, "communication": {"protocol": "stdio", "timeout": 120000}, "refreshIntervalCron": "0 */4 * * *", "capabilities": {"systemPromptPlaceholders": [{"placeholder": "{{VCPDailyHot}}", "description": "包含所有主流平台（微博、知乎、B站等）实时热榜的综合信息。此占位符会周期性更新。"}]}}