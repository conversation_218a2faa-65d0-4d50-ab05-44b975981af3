const fsPromises = require('fs').promises;
const fs = require('fs');
const path = require('path');
const dotenv = require('dotenv');

// 加载插件专属的 .env 文件
const envPath = path.join(__dirname, 'config.env');
if (fs.existsSync(envPath)) {
    dotenv.config({ path: envPath });
}

const LOG_PREFIX = '[ScopusSearch]';
const CACHE_FILE = path.join(__dirname, 'scopus_papers_cache.json');
const PDF_LOG_FILE = path.join(__dirname, 'pdf_download_log.json');

/**
 * Scopus学术搜索插件 - VCP Hybridservice模式
 * 支持静态推送和实时工具调用两种功能
 */
class ScopusSearchPlugin {
    constructor() {
        this.config = this.loadConfig();
        this.debugMode = this.config.SCOPUS_DEBUG_MODE || false;
        this.staticPlaceholderData = null;
        this.pdfDownloadLog = [];
        
        if (this.debugMode) {
            console.error(`${LOG_PREFIX} Plugin initialized with config:`, JSON.stringify(this.config, null, 2));
        }
    }

    /**
     * 加载配置信息
     */
    loadConfig() {
        return {
            SCOPUS_API_KEY: process.env.SCOPUS_API_KEY || '',
            SCOPUS_SEARCH_TERMS: process.env.SCOPUS_SEARCH_TERMS || '"artificial intelligence" OR "machine learning" OR "deep learning"',
            SCOPUS_MAX_RESULTS: parseInt(process.env.SCOPUS_MAX_RESULTS, 10) || 50,
            SCOPUS_DAYS_RANGE: parseInt(process.env.SCOPUS_DAYS_RANGE, 10) || 7,
            PDF_DOWNLOAD_ENABLED: (process.env.PDF_DOWNLOAD_ENABLED || 'true').toLowerCase() === 'true',
            PDF_DOWNLOAD_PATH: process.env.PDF_DOWNLOAD_PATH || './scopus_papers',
            THIRD_PARTY_ENABLED: (process.env.THIRD_PARTY_ENABLED || 'false').toLowerCase() === 'true',
            SCIHUB_MIRROR: process.env.SCIHUB_MIRROR || 'https://sci-hub.se',
            SCOPUS_DEBUG_MODE: (process.env.SCOPUS_DEBUG_MODE || 'false').toLowerCase() === 'true'
        };
    }

    /**
     * 获取当前时间戳
     */
    getTimestamp() {
        return new Date().toISOString();
    }

    /**
     * 格式化日期为Scopus API格式
     */
    formatDateForScopus(date) {
        return date.toISOString().split('T')[0];
    }

    /**
     * 清理和标准化文本
     */
    cleanText(text) {
        if (typeof text !== 'string') return 'N/A';
        return text.replace(/\s+/g, ' ').trim();
    }

    /**
     * 调用Scopus API进行搜索
     */
    async callScopusAPI(searchParams) {
        const dns = require('dns').promises;

        try {
            if (this.debugMode) {
                console.log(`${LOG_PREFIX} [Network Test] Resolving api.elsevier.com...`);
            }
            const address = await dns.lookup('api.elsevier.com');
            if (this.debugMode) {
                console.log(`${LOG_PREFIX} [Network Test] api.elsevier.com resolved to:`, address);
            }
        } catch (dnsError) {
            console.error(`${LOG_PREFIX} [Network Test] DNS lookup for api.elsevier.com failed:`, dnsError);
            throw new Error(`DNS lookup failed: ${dnsError.message}`);
        }

        if (!this.config.SCOPUS_API_KEY || this.config.SCOPUS_API_KEY === 'your_scopus_api_key_here') {
            if (this.debugMode) {
                console.error(`${LOG_PREFIX} Scopus API key not configured. Returning mock data for testing.`);
            }
            // 返回一个模拟的API响应数据
            return {
                'search-results': {
                    'opensearch:totalResults': '1',
                    'entry': [
                        {
                            'dc:identifier': 'SCOPUS_ID:85086241777',
                            'dc:title': 'This is a mock paper about ' + searchParams.query,
                            'dc:creator': 'Mock Author',
                            'prism:publicationName': 'Journal of Mock Research',
                            'prism:coverDate': '2025-01-01',
                            'prism:doi': '10.1000/mock-doi',
                            'dc:description': 'This is a mock abstract for a paper about ' + searchParams.query + '. The purpose of this mock data is to test the plugin without a real API key.',
                            'citedby-count': '42',
                            'link': [
                                { '@ref': 'scopus', '@href': 'https://www.scopus.com/inward/record.uri?partnerID=40&md5=...&rel=self' }
                            ]
                        }
                    ]
                }
            };
        }

        const fetch = (await import('node-fetch')).default;
        
        // 构建查询参数
        const params = new URLSearchParams({
            query: searchParams.query,
            count: searchParams.max_results || this.config.SCOPUS_MAX_RESULTS,
            start: searchParams.start || 0,
            field: 'doi,title,creator,publicationName,coverDate,description,link,citedby-count'
        });

        // 添加日期范围过滤
        if (searchParams.date_range) {
            const [startDate, endDate] = searchParams.date_range.split(':');
            if (startDate && endDate) {
                params.append('date', `${startDate}-${endDate}`);
            }
        }

        const url = `https://api.elsevier.com/content/search/scopus?${params.toString()}`;
        
        if (this.debugMode) {
            console.error(`${LOG_PREFIX} Calling Scopus API: ${url}`);
        }

        const response = await fetch(url, {
            headers: {
                'X-ELS-APIKey': this.config.SCOPUS_API_KEY,
                'Accept': 'application/json',
                'User-Agent': 'VCPToolBox-ScopusSearch/1.0'
            }
        });

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`Scopus API request failed: ${response.status} - ${errorText}`);
        }

        const data = await response.json();
        return data;
    }

    /**
     * 解析Scopus API响应数据
     */
    parseScopusResponse(apiResponse) {
        if (!apiResponse['search-results'] || !apiResponse['search-results'].entry) {
            return [];
        }

        return apiResponse['search-results'].entry.map(entry => {
            // 提取作者信息
            const authors = entry.author ? 
                entry.author.map(a => a.authname).join('; ') : 'N/A';

            // 提取DOI
            const doi = entry['prism:doi'] || 'N/A';

            // 提取期刊信息
            const journal = entry['prism:publicationName'] || 'N/A';

            // 提取发表日期
            const publicationDate = entry['prism:coverDate'] || 'N/A';

            // 提取摘要
            const abstract = this.cleanText(entry['dc:description'] || 'N/A');

            // 提取标题
            const title = this.cleanText(entry['dc:title'] || 'N/A');

            // 提取引用数
            const citedByCount = parseInt(entry['citedby-count'], 10) || 0;

            // 提取链接
            const links = entry.link || [];
            const scopusLink = links.find(l => l['@ref'] === 'scopus') || links[0];
            const link = scopusLink ? scopusLink['@href'] : 'N/A';

            return {
                scopus_id: entry['dc:identifier'] || 'N/A',
                title: title,
                authors: authors,
                publication_date: publicationDate,
                abstract: abstract,
                doi: doi,
                journal: journal,
                cited_by_count: citedByCount,
                link: link,
                pdf_status: 'not_attempted',
                pdf_path: null,
                download_source: 'none',
                retrieved_at: this.getTimestamp()
            };
        });
    }

    /**
     * 尝试下载PDF文件 - 三层级策略
     */
    async downloadPDF(paper) {
        if (!this.config.PDF_DOWNLOAD_ENABLED) {
            return { ...paper, pdf_status: 'disabled' };
        }

        try {
            // 确保PDF下载目录存在
            await fsPromises.mkdir(this.config.PDF_DOWNLOAD_PATH, { recursive: true });

            // 生成安全的文件名
            const safeTitle = paper.title.replace(/[^a-zA-Z0-9\s-]/g, '').substring(0, 100);
            const safeDoi = paper.doi.replace(/[^a-zA-Z0-9-_.]/g, '_');
            const filename = `${safeDoi}_${safeTitle}.pdf`;
            const filepath = path.join(this.config.PDF_DOWNLOAD_PATH, filename);

            // 检查文件是否已存在
            try {
                await fsPromises.access(filepath);
                return {
                    ...paper,
                    pdf_status: 'already_exists',
                    pdf_path: filepath,
                    download_source: 'cache'
                };
            } catch (error) {
                // 文件不存在，继续下载流程
            }

            // 第一层：尝试官方开放获取
            const officialResult = await this.tryOfficialPDFDownload(paper, filepath);
            if (officialResult.success) {
                return {
                    ...paper,
                    pdf_status: 'downloaded',
                    pdf_path: filepath,
                    download_source: 'official'
                };
            }

            // 第二层：尝试第三方服务（如果启用）
            if (this.config.THIRD_PARTY_ENABLED) {
                const thirdPartyResult = await this.tryThirdPartyPDFDownload(paper, filepath);
                if (thirdPartyResult.success) {
                    return {
                        ...paper,
                        pdf_status: 'downloaded',
                        pdf_path: filepath,
                        download_source: 'third_party'
                    };
                }
            }

            // 第三层：仅提供论文信息
            return {
                ...paper,
                pdf_status: 'unavailable',
                pdf_path: null,
                download_source: 'none'
            };

        } catch (error) {
            if (this.debugMode) {
                console.error(`${LOG_PREFIX} PDF download error for ${paper.title}:`, error.message);
            }
            return {
                ...paper,
                pdf_status: 'error',
                pdf_path: null,
                download_source: 'none',
                error_message: error.message
            };
        }
    }

    /**
     * 尝试官方PDF下载
     */
    async tryOfficialPDFDownload(paper, filepath) {
        // 这里实现官方PDF下载逻辑
        // 通常需要检查开放获取状态和直接链接
        if (this.debugMode) {
            console.error(`${LOG_PREFIX} Attempting official PDF download for: ${paper.title}`);
        }

        // 模拟实现 - 实际需要根据Scopus API文档实现
        return { success: false, reason: 'Official download not implemented yet' };
    }

    /**
     * 尝试第三方PDF下载
     */
    async tryThirdPartyPDFDownload(paper, filepath) {
        if (paper.doi === 'N/A') {
            return { success: false, reason: 'No DOI available' };
        }

        try {
            const fetch = (await import('node-fetch')).default;
            const scihubUrl = `${this.config.SCIHUB_MIRROR}/${paper.doi}`;
            
            if (this.debugMode) {
                console.error(`${LOG_PREFIX} Attempting third-party PDF download from: ${scihubUrl}`);
            }

            // 模拟实现 - 实际需要解析Sci-Hub页面获取PDF链接
            // 注意：使用第三方服务需要考虑法律合规性
            return { success: false, reason: 'Third-party download not implemented yet' };

        } catch (error) {
            return { success: false, reason: error.message };
        }
    }

    /**
     * 缓存数据到文件
     */
    async cacheData(data) {
        try {
            await fsPromises.writeFile(CACHE_FILE, JSON.stringify(data, null, 2), 'utf8');
            if (this.debugMode) {
                console.error(`${LOG_PREFIX} Data cached to ${CACHE_FILE}`);
            }
        } catch (error) {
            console.error(`${LOG_PREFIX} Failed to cache data:`, error.message);
            throw error;
        }
    }

    /**
     * 从缓存加载数据
     */
    async loadFromCache() {
        try {
            const data = await fsPromises.readFile(CACHE_FILE, 'utf8');
            return JSON.parse(data);
        } catch (error) {
            if (error.code !== 'ENOENT') {
                console.error(`${LOG_PREFIX} Failed to load cache:`, error.message);
            }
            return null;
        }
    }

    /**
     * 静态功能：获取并缓存论文数据
     */
    async fetchStaticData() {
        try {
            if (this.debugMode) {
                console.error(`${LOG_PREFIX} Starting static data fetch`);
            }

            // 构建日期范围
            const endDate = new Date();
            const startDate = new Date();
            startDate.setDate(endDate.getDate() - this.config.SCOPUS_DAYS_RANGE);

            const searchParams = {
                query: this.config.SCOPUS_SEARCH_TERMS,
                max_results: this.config.SCOPUS_MAX_RESULTS,
                date_range: `${this.formatDateForScopus(startDate)}:${this.formatDateForScopus(endDate)}`
            };

            const apiResponse = await this.callScopusAPI(searchParams);
            const papers = this.parseScopusResponse(apiResponse);

            // 处理PDF下载（可选）
            const processedPapers = [];
            for (const paper of papers) {
                const processedPaper = this.config.PDF_DOWNLOAD_ENABLED ? 
                    await this.downloadPDF(paper) : paper;
                processedPapers.push(processedPaper);
            }

            // 缓存数据
            await this.cacheData(processedPapers);
            this.staticPlaceholderData = processedPapers;

            if (this.debugMode) {
                console.error(`${LOG_PREFIX} Static data fetch completed. Found ${processedPapers.length} papers`);
            }

            return processedPapers;

        } catch (error) {
            console.error(`${LOG_PREFIX} Static data fetch failed:`, error.message);
            
            // 尝试从缓存加载
            const cachedData = await this.loadFromCache();
            if (cachedData) {
                this.staticPlaceholderData = cachedData;
                if (this.debugMode) {
                    console.error(`${LOG_PREFIX} Using cached data instead`);
                }
                return cachedData;
            }
            
            throw error;
        }
    }

    /**
     * 同步功能：处理工具调用请求
     */
    async processToolCall(params) {
        try {
            if (this.debugMode) {
                console.error(`${LOG_PREFIX} Processing tool call with params:`, params);
            }

            // 验证必需参数
            if (!params.query) {
                throw new Error('Query parameter is required');
            }

            // 构建搜索参数
            const searchParams = {
                query: params.query,
                max_results: parseInt(params.max_results, 10) || 20,
                start: parseInt(params.start, 10) || 0
            };

            // 添加可选参数
            if (params.author) {
                searchParams.query += ` AND AUTHOR-NAME(${params.author})`;
            }

            if (params.journal) {
                searchParams.query += ` AND SRCTITLE(${params.journal})`;
            }

            if (params.date_range) {
                searchParams.date_range = params.date_range;
            }

            // 调用Scopus API
            const apiResponse = await this.callScopusAPI(searchParams);
            const papers = this.parseScopusResponse(apiResponse);

            // 处理PDF下载（如果请求）
            const downloadPdf = (params.download_pdf || 'false').toLowerCase() === 'true';
            const processedPapers = [];
            
            if (downloadPdf && this.config.PDF_DOWNLOAD_ENABLED) {
                for (const paper of papers) {
                    const processedPaper = await this.downloadPDF(paper);
                    processedPapers.push(processedPaper);
                }
            } else {
                processedPapers.push(...papers);
            }

            if (this.debugMode) {
                console.error(`${LOG_PREFIX} Tool call completed. Found ${processedPapers.length} papers`);
            }

            return {
                success: true,
                data: processedPapers,
                total_results: apiResponse['search-results']['opensearch:totalResults'] || processedPapers.length,
                query_used: searchParams.query,
                timestamp: this.getTimestamp()
            };

        } catch (error) {
            console.error(`${LOG_PREFIX} Tool call failed:`, error.message);
            return {
                success: false,
                error: error.message,
                timestamp: this.getTimestamp()
            };
        }
    }

}

// 创建插件实例
const plugin = new ScopusSearchPlugin();

// 导出VCP所需的方法 (stdio 模式下不需要)
// module.exports = {
//     initialize: plugin.initialize.bind(plugin),
//     processMessages: plugin.processMessages.bind(plugin),
//     getStaticData: plugin.getStaticData.bind(plugin),
//     shutdown: plugin.shutdown.bind(plugin)
// };

// stdio 协议的入口点
if (require.main === module) {
    const chunks = [];
    process.stdin.on('data', chunk => chunks.push(chunk));
    process.stdin.on('end', async () => {
        const inputData = Buffer.concat(chunks).toString('utf8');
        if (!inputData) {
            console.error(JSON.stringify({
                status: "error",
                error: "No input data received via stdin."
            }));
            process.exit(1);
            return;
        }

        try {
            const params = JSON.parse(inputData);
            const result = await plugin.processToolCall(params);
            // 将最终结果作为标准JSON输出
            console.log(JSON.stringify({
                status: result.success ? "success" : "error",
                result: result.success ? result : null,
                error: result.success ? null : result.error,
                messageForAI: result.success ? `Scopus search completed. Found ${result.data.length} results.` : `Scopus search failed: ${result.error}`
            }));
            process.exit(0);
        } catch (error) {
            console.error(JSON.stringify({
                status: "error",
                error: `Error processing input: ${error.message}`,
                input: inputData.substring(0, 200) // Log part of the input for debugging
            }));
            process.exit(1);
        }
    });
}